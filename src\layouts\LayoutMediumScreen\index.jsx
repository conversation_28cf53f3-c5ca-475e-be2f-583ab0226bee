import React, { useMemo, memo, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DNotificationHeader from '@/components/DNotificationHeader';
import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DProgressBar from '@/components/Global/DProgressBar';
import BookIcon from '@/components/Global/Icons/BookIcon';
import ZapIcon from '@/components/Global/Icons/ZapIcon';
import HeaderAccountMenu from '@/components/HeaderAccountMenu';
import MainNav from '@/components/MainNav';
import HeaderLearningHub from '../../components/HeaderLearningHub';
import DToastContainer from '@/components/DToast/DToastContainer';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import UserGroupIcon from '@/components/Global/Icons/UserGroupIcon';
import UserPlusIcon from '@/components/Global/Icons/UserPlusIcon';
import InviteTeamMember from '@/components/InviteTeamMember';
import ReferralProgram from '@/components/ReferralProgram';
import { useUserStore } from '@/stores/user/userStore';
import UpgradePlanIcon from '@/components/Global/Icons/UpgradePlanIcon';
import DModalPlans from '@/components/DModalPlans';
import useLayoutStore from '@/stores/layout/layoutStore';
/**
 * This layout component displays the children content
 * when the device screen size is equal to or larger than the `sm` breakpoint.
 */
const LayoutMediumScreen = ({ children, title, progressBar }) => {
  const selectedTeam = useTeamManagementStore((state) => state.selectedTeam);
  const user = useUserStore((state) => state.user);
  const navigate = useNavigate();
  const userData = useUserStore((state) => state.user);
  const { planModal, setPlanModal } = useLayoutStore((state) => state);

  useEffect(() => {
    setPlanModal({
      show: window.location.pathname !== '/plans' && !selectedTeam,
    });
  }, [selectedTeam, setPlanModal, window.location.pathname]);

  const addPlanButton = useMemo(() => {
    if (!planModal.show) return null;

    return (
      <DButton
        className="!gap-size0 bg-white"
        onClick={() =>
          setPlanModal({
            isOpen: true,
          })
        }
      >
        <UpgradePlanIcon className="mb-[3.5px]" />
        <span>{userData?.new_design_trial ? 'Add plan' : 'Membership'}</span>
      </DButton>
    );
  }, [planModal.show, userData?.new_design_trial]);

  return (
    <>
      <main className="layout-medium-screen d-h-screen flex flex-row gap-size2 xl:gap-size5 w-full p-size3">
        <MainNav />

        <div className="w-full flex flex-col h-full gap-size2">
          <header className="flex justify-between pb-[30px] pt-size1">
            <div className="flex gap-size1 w-full md:max-w-[70%] items-center">
              {title && <h1 className="text-[22px] w-[300px] h-[14px] ">{title}</h1>}
              {progressBar && progressBar.length > 0 && (
                <DProgressBar steps={progressBar} />
              )}
            </div>

            <DToastContainer hideInMobile />

            <div className="flex items-center justify-end">
              <div className="flex gap-size2 items-center mr-[10px]">
                {addPlanButton}
                {user?.id && <InviteTeamMember />}
                {/* {user?.id && <ReferralProgram />} */}
              </div>

              <div className="flex mr-[2px]">
                <DNotificationHeader />
                <HeaderLearningHub />
              </div>
              <div className="flex ml-[10px]">
                <HeaderAccountMenu />
              </div>
            </div>
          </header>
          <div className="h-[1px] flex flex-col grow">{children}</div>
        </div>
      </main>
      <DModalPlans
        isOpen={planModal.isOpen}
        onClose={() =>
          setPlanModal({
            isOpen: false,
          })
        }
      />
    </>
  );
};

// Use memo to prevent unnecessary re-renders
export default memo(LayoutMediumScreen);
