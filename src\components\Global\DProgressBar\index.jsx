import React from 'react';

import <PERSON>pin<PERSON> from '../DSpinner';
import CheckIcon from '../Icons/CheckIcon';
import FinishLineIcon from '../Icons/FinishLineIcon';

const DProgressBar = ({ steps }) => {
  return (
    <div className="flex flex-col w-full">
      <div className="w-full overflow-x-auto lg:overflow-x-visible scrollbar lg:no-scrollbar">
        <div className="flex items-center min-w-full sm:w-full">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="flex items-end h-10 md:h-7 min-w-[80px] sm:min-w-[100px] md:min-w-0 md:w-full flex-shrink-0 sm:flex-shrink">
                <div className="flex flex-col items-center w-full gap-size1">
                  <div
                    className={`text-2xs sm:text-xs text-center leading-tight text-black px-1 ${
                      step.active
                        ? 'font-medium text-darkGreen-300'
                        : step.completed
                        ? 'font-medium text-darkGreen-300'
                        : 'text-gray-400'
                    }`}
                    style={{
                      wordBreak: 'break-word',
                      hyphens: 'auto',
                      lineHeight: '1.2'
                    }}
                  >
                    {step.progress_label}
                  </div>
                  {index <= steps.length - 1 && (
                    <div className="w-full h-1 bg-gray-300">
                      {step.completed && (
                        <div className="w-full h-full bg-green-500"></div>
                      )}
                      {step.active && (
                        <div className="w-1/2 h-full bg-green-500"></div>
                      )}
                    </div>
                  )}
                </div>
                <div className="translate-y-[10px] ml-1 sm:ml-0">
                  {step.completed && index !== steps.length - 1 && (
                    <div className="w-6 h-6 sm:w-7 sm:h-7 bg-darkGreen-10 rounded-full flex items-center justify-center flex-shrink-0 mx-size0">
                      <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4 text-darkGreen-300" />
                    </div>
                  )}
                  {step.active && index !== steps.length - 1 && (
                    <div className="translate-y-[6px] sm:translate-y-[8px]">
                      <DSpinner
                        width={36}
                        height={36}
                        className="text-darkGreen-300 flex-shrink-0 sm:w-[42px] sm:h-[42px]"
                      />
                    </div>
                  )}
                  {(step.pending || step.active) &&
                    index === steps.length - 1 && (
                      <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full flex items-center justify-center flex-shrink-0 mx-size0">
                        <FinishLineIcon className="w-4 h-4 sm:w-5 sm:h-5 text-black" />
                      </div>
                    )}

                  {step.completed && index === steps.length - 1 && (
                    <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full flex items-center justify-center flex-shrink-0 mx-size0">
                      <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4 text-black" />
                    </div>
                  )}

                  {step.pending && index !== steps.length - 1 && (
                    <div className="translate-y-[6px] sm:translate-y-[8px]">
                      <DSpinner
                        width={36}
                        height={36}
                        className="text-gray-400 flex-shrink-0 sm:w-[42px] sm:h-[42px]"
                      />
                    </div>
                  )}
                </div>
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DProgressBar;
